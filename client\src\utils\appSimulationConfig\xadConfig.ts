// TODO: fetch data from db

import {
  AppSimulationScreen,
  AppSimulationScreenElement,
  AppSimulationScreenPlaceholder,
} from '~/common';
import { CalculationConfig } from '~/components/JobSimulation/AppSimulation/CalculationEngine';

// X Ad Calculation Configuration
export const xAdCalculationConfig: CalculationConfig = {
  rules: {
    estimated_accounts: {
      id: 'estimated_accounts',
      type: 'range',
      inputs: ['budget', 'audience_location', 'audience_age', 'audience_gender'],
      baseValue: 500,
      multipliers: {
        budget: { type: 'linear', factor: 1 },
        audience_location: { type: 'array_length', factor: 1.5 },
        audience_age: { type: 'range_width', maxRange: 47, minValue: 0.6 },
        audience_gender: {
          type: 'conditional',
          conditions: [
            { condition: 'value === "All"', value: 1.5 },
            { condition: 'value === "Men"', value: 0.8 },
            { condition: 'value === "Women"', value: 0.7 },
          ],
        },
      },
      formatters: [{ type: 'number' }],
    },
    estimated_post_engagement: {
      id: 'estimated_post_engagement',
      type: 'range',
      inputs: ['budget', 'audience_location', 'audience_age', 'audience_gender'],
      baseValue: 100,
      multipliers: {
        budget: { type: 'linear', factor: 1 },
        audience_location: { type: 'array_length', factor: 1.5 },
        audience_age: { type: 'range_width', maxRange: 47, minValue: 0.6 },
        audience_gender: {
          type: 'conditional',
          conditions: [
            { condition: 'value === "All"', value: 1.5 },
            { condition: 'value === "Men"', value: 0.8 },
            { condition: 'value === "Women"', value: 0.7 },
          ],
        },
      },
      formatters: [{ type: 'number' }],
    },
    total_budget: {
      id: 'total_budget',
      type: 'formula',
      inputs: ['budget'],
      formula: 'budget * 7',
      formatters: [{ type: 'currency' }],
    },
    budget_per_day: {
      id: 'budget_per_day',
      type: 'formula',
      inputs: ['budget'],
      formula: 'budget',
      formatters: [{ type: 'custom', template: '${value}.00 a day x 7 days' }],
    },
    estimated_vat: {
      id: 'estimated_vat',
      type: 'formula',
      inputs: ['total_budget_raw'],
      formula: 'total_budget_raw * 0.05',
      formatters: [{ type: 'currency' }],
    },
    total_budget_raw: {
      id: 'total_budget_raw',
      type: 'formula',
      inputs: ['budget'],
      formula: 'budget * 7',
    },
    total_amount: {
      id: 'total_amount',
      type: 'formula',
      inputs: ['total_budget_raw', 'estimated_vat_raw'],
      formula: 'total_budget_raw + estimated_vat_raw',
      formatters: [{ type: 'currency' }],
    },
    estimated_vat_raw: {
      id: 'estimated_vat_raw',
      type: 'formula',
      inputs: ['total_budget_raw'],
      formula: 'total_budget_raw * 0.05',
    },
  },
  dependencies: {
    budget: [
      'estimated_accounts',
      'estimated_post_engagement',
      'total_budget',
      'budget_per_day',
      'total_budget_raw',
    ],
    audience_location: ['estimated_accounts', 'estimated_post_engagement'],
    audience_age: ['estimated_accounts', 'estimated_post_engagement'],
    audience_gender: ['estimated_accounts', 'estimated_post_engagement'],
    total_budget_raw: ['estimated_vat', 'estimated_vat_raw', 'total_amount'],
    estimated_vat_raw: ['total_amount'],
  },
  defaultValues: {
    budget: 1,
    audience_location: ['Vietnam'],
    audience_age: [18, 65],
    audience_gender: 'All',
  },
};

// const budgetDropdownOptions = [
//   {
//     label: '$1.00',
//     value: 1,
//     screenId: '005',
//     dataContextId: 'budget',
//     dataContext: 'Budget selected: $1.00 per day',
//     saveToSelections: true,
//   },
//   {
//     label: '$10.00',
//     value: 10,
//     screenId: '005_03',
//     dataContextId: 'budget',
//     dataContext: 'Budget selected: $10.00 per day',
//     saveToSelections: true,
//   },
// ];

const campaignDetailsScreens = {
  elements: [
    {
      x1: 66.*************,
      y1: 18.**************,
      x2: 68.**************,
      y2: 20.**************,
      title: 'Choose Objective',
      action: { type: 'nextScreen', screenId: '001_03' },
    },
    {
      x1: 42.049050632911396,
      y1: 26.430339312821637,
      x2: 68.86867088607595,
      y2: 29.245405038447615,
      title: 'Campaign Name',
      action: {
        type: 'inputText',
        inputType: 'input',
        dataContextId: 'campaign_name',
        dataContextLabel: 'Campaign name',
        saveToSelections: true,
      },
    },
    {
      x1: 42.049050632911396,
      y1: 46.91776209376622,
      x2: 55.02373417721519,
      y2: 49.7328278193922,
      title: 'Daily Campaign Budget',
      action: {
        type: 'inputText',
        inputType: 'input',
        dataContextId: 'daily_campaign_budget',
        dataContextLabel: 'Daily Campaign Budget',
        saveToSelections: true,
      },
    },
    {
      x1: 55.97310126582279,
      y1: 46.91776209376622,
      x2: 68.90822784810126,
      y2: 49.7328278193922,
      title: 'Total Campaign Budget',
      action: {
        type: 'inputText',
        inputType: 'input',
        dataContextId: 'total_campaign_budget',
        dataContextLabel: 'Total Campaign Budget',
        saveToSelections: true,
      },
    },
    {
      x1: 42.84018987341772,
      y1: 56.84868840361341,
      x2: 51.78006329113924,
      y2: 59.11638023814544,
      title: 'Standard Pacing',
      action: { type: 'nextScreen', screenId: '001_01' },
    },
    {
      x1: 42.879746835443036,
      y1: 59.377034074272906,
      x2: 48.10126582278481,
      y2: 61.097352017710996,
      title: 'Accelerated Pacing',
      action: { type: 'nextScreen', screenId: '001_02' },
    },
    {
      x1: 96.4003164556962,
      y1: 96.207476124699,
      x2: 99.01107594936708,
      y2: 99.2571306607938,
      title: 'next',
      action: { type: 'nextScreen', screenId: '002_01' },
    },
  ],
  placeholders: [
    {
      id: 'objective',
      type: 'text',
      dataId: 'objective',
      initialValue: 'Engagement',
      x1: 42.36550632911392,
      y1: 17.281375704537226,
      x2: 61.03639240506329,
      y2: 22.1295444542264,
      style: {
        fontWeight: 'bold',
      },
    },
    {
      id: 'input_campaign_name',
      type: 'text',
      dataId: 'campaign_name',
      x1: 42.24683544303797,
      y1: 26.508535582977917,
      x2: 68.71044303797468,
      y2: 29.08901249813506,
      initialValue: 'Untitled',
    },
    {
      id: 'campaign_name',
      type: 'text',
      dataId: 'campaign_name',
      x1: 0.7515822784810127,
      y1: 6.568486693127272,
      x2: 9.533227848101266,
      y2: 10.791085281566232,
      initialValue: 'Untitled',
    },
    {
      id: 'daily_campaign_budget',
      type: 'text',
      dataId: 'daily_campaign_budget',
      x1: 44.264240506329116,
      y1: 46.995958363922504,
      x2: 54.82594936708861,
      y2: 49.65463154923592,
      initialValue: '0.00',
    },
    {
      id: 'total_campaign_budget',
      type: 'text',
      dataId: 'total_campaign_budget',
      x1: 58.18829113924051,
      y1: 47.07415463407878,
      x2: 68.71044303797468,
      y2: 49.57643527907965,
      initialValue: '0.00',
    },
  ],
};

export const xAdScreens: AppSimulationScreen[] = [
  {
    id: '000',
    title: '',
    image: '/assets/job-simulation/xad/000.png',
    bgColor: 'bg-[#64767e]',
    elements: [
      {
        x1: 77.09651898734177,
        y1: 0.7035830842701256,
        x2: 81.44778481012658,
        y2: 3.7524431161073366,
        title: 'Get Started',
        action: { type: 'nextScreen', screenId: '001_01' },
      },
      {
        x1: 41.890822784810126,
        y1: 33.0684049606959,
        x2: 48.85284810126582,
        y2: 36.66449628029877,
        title: 'Launch a campaign',
        action: { type: 'nextScreen', screenId: '001_01' },
      },
    ],
  },
  {
    id: '001_01',
    title: 'Campaign Details',
    image: '/assets/job-simulation/xad/001_01.png',
    bgColor: 'bg-[#64767e]',
    elements: campaignDetailsScreens.elements as AppSimulationScreenElement[],
    placeholders: campaignDetailsScreens.placeholders as AppSimulationScreenPlaceholder[],
  },
  {
    id: '001_02',
    title: 'Campaign Details',
    image: '/assets/job-simulation/xad/001_02.png',
    bgColor: 'bg-[#64767e]',
    elements: campaignDetailsScreens.elements as AppSimulationScreenElement[],
    placeholders: campaignDetailsScreens.placeholders as AppSimulationScreenPlaceholder[],
  },
  {
    id: '001_03',
    title: '',
    image: '/assets/job-simulation/xad/001_03.png',
    bgColor: 'bg-[#64767e]',
    elements: [
      {
        x1: 44.264240506329116,
        y1: 63.88212459436476,
        x2: 55.73575949367089,
        y2: 72.23834382614598,
        title: 'Objective Engagement',
        actions: [
          {
            type: 'previousScreen',
            dataContext: 'Engagement',
            dataContextId: 'objective',
            dataContextLabel: 'Objective',
            saveToSelections: true,
          },
        ],
      },
      {
        x1: 44.303797468354425,
        y1: 55.76019188310079,
        x2: 55.73575949367089,
        y2: 62.86688300545678,
        title: 'Objective Website Traffic',
        actions: [
          {
            type: 'previousScreen',
            dataContext: 'Website Traffic',
            dataContextId: 'objective',
            dataContextLabel: 'Objective',
            saveToSelections: true,
          },
        ],
      },
      {
        x1: 32.00158227848101,
        y1: 31.31629824246977,
        x2: 43.43354430379747,
        y2: 38.50108487166483,
        title: 'Objective Reach',
        actions: [
          {
            type: 'previousScreen',
            dataContext: 'Reach',
            dataContextId: 'objective',
            dataContextLabel: 'Objective',
            saveToSelections: true,
          },
        ],
      },
    ],
    placeholders: [
      {
        id: 'campaign_name',
        type: 'text',
        dataId: 'campaign_name',
        x1: 0.7515822784810127,
        y1: 6.568486693127272,
        x2: 9.533227848101266,
        y2: 10.791085281566232,
        initialValue: 'Untitled',
        style: {
          backgroundColor: '#3f4347',
          color: '#000',
          fontWeight: '300',
        },
      },
    ],
  },
  {
    id: '002_01',
    title: '',
    image: '/assets/job-simulation/xad/002_01.png',
    bgColor: 'bg-[#64767e]',
    elements: [
      {
        x1: 34.6123417721519,
        y1: 19.700326359563515,
        x2: 36.985759493670884,
        y2: 22.51465869664402,
        title: 'Gender: Any',
        action: {
          type: 'image',
          imgPath: '/assets/job-simulation/xad/gender/gender_any.png',
          dataContext: 'Any',
          dataContextId: 'gender',
          dataContextLabel: 'Gender',
          saveToSelections: true,
        },
      },
      {
        x1: 37.064873417721515,
        y1: 19.700326359563515,
        x2: 40.348101265822784,
        y2: 22.51465869664402,
        title: 'Gender: Women',
        action: {
          type: 'image',
          imgPath: '/assets/job-simulation/xad/gender/gender_women.png',
          dataContext: 'Women',
          dataContextId: 'gender',
          dataContextLabel: 'Gender',
          saveToSelections: true,
        },
      },
      {
        x1: 40.3876582278481,
        y1: 19.700326359563515,
        x2: 42.84018987341772,
        y2: 22.51465869664402,
        title: 'Gender: Men',
        action: {
          type: 'image',
          imgPath: '/assets/job-simulation/xad/gender/gender_men.png',
          dataContext: 'Men',
          dataContextId: 'gender',
          dataContextLabel: 'Gender',
          saveToSelections: true,
        },
      },
      {
        x1: 34.375,
        y1: 28.534202862066206,
        x2: 38.528481012658226,
        y2: 30.644952114876585,
        title: 'Age',
        action: {
          type: 'modal',
          modalConfig: {
            title: 'Age Range',
            description: 'Select the age range.',
            inputs: [
              {
                id: 'age',
                type: 'range',
                label: 'Age Range',
                dataId: 'audience_age',
                min: 18,
                max: 65,
                step: 1,
                defaultValue: [18, 65],
                labels: { min: '18', max: '65+' },
                formatType: 'age-range',
                formatConfig: {
                  maxValue: 65,
                  maxLabel: '65+',
                  separator: ' - ',
                },
              },
            ],
          },
        },
      },
      {
        x1: 34.572784810126585,
        y1: 38.931597329613616,
        x2: 61.5506329113924,
        y2: 41.74592966669412,
        title: 'Language',
        action: {
          type: 'modal',
          modalConfig: {
            title: 'Language',
            description: 'Select languages',
            inputs: [
              {
                id: 'language',
                type: 'multiSelect',
                label: 'Languages',
                dataId: 'audience_language',
                options: [
                  'English',
                  'Korean',
                  'French',
                  'Thai',
                  'Vietnamese',
                  'Chinese',
                  'Japanese',
                  'Indonesian',
                ],
                required: true,
                minSelections: 1,
                defaultValue: ['English'],
              },
            ],
          },
        },
      },
      {
        x1: 34.572784810126585,
        y1: 56.91205392762794,
        x2: 61.5506329113924,
        y2: 59.88273806121291,
        title: 'Location',
        action: {
          type: 'modal',
          modalConfig: {
            title: 'Language',
            description: 'Select locations',
            inputs: [
              {
                id: 'language',
                type: 'multiSelect',
                label: 'Locations',
                dataId: 'audience_location',
                options: [
                  'Hong Kong',
                  'Republic of Korea',
                  'France',
                  'Thailand',
                  'Vietnam',
                  'China',
                  'Japan',
                  'Indonesia',
                  'Singapore',
                  'Australia',
                ],
                required: true,
                minSelections: 1,
                defaultValue: ['English'],
              },
            ],
          },
        },
      },
      {
        x1: 34.375,
        y1: 87.47883014425229,
        x2: 36.59018987341772,
        y2: 89.04234810929701,
        title: 'OS: iOS',
        action: {
          dataContext: 'iOS',
          dataContextId: 'operation_system',
          dataContextLabel: 'Operation System',
          saveToSelections: true,
        },
      },
      {
        x1: 34.335443037974684,
        y1: 89.19869990580148,
        x2: 37.89556962025317,
        y2: 90.60586607434173,
        title: 'OS: Android',
        action: {
          dataContext: 'android',
          dataContextId: 'operation_system',
          dataContextLabel: 'Operation System',
          saveToSelections: true,
        },
      },
      {
        x1: 11.867088607594937,
        y1: 96.28664841162741,
        x2: 14.675632911392405,
        y2: 99.25733254521238,
        title: 'Back',
        action: {
          type: 'previousScreen',
        },
      },
    ],
    placeholders: [
      {
        id: 'campaign_name',
        x1: 0.8306962025316454,
        y1: 6.801303147944547,
        x2: 9.612341772151899,
        y2: 10.866449857060829,
        type: 'text',
        dataId: 'campaign_name',
        initialValue: 'Untitled',
      },
      {
        id: 'ad_name',
        x1: 1.1075949367088607,
        y1: 20.794788935094825,
        x2: 10.403481012658228,
        y2: 24.15635255994098,
        type: 'text',
        dataId: 'ad_name',
        initialValue: 'Untitled',
      },
      {
        id: 'estimated_audience_size',
        x1: 63.96360759493671,
        y1: 16.651466327726304,
        x2: 76.97784810126582,
        y2: 20.560261240338114,
        type: 'text',
        dataId: 'estimated_audience_size',
      },
      {
        id: 'gender',
        x1: 34.572784810126585,
        y1: 19.62215046131128,
        x2: 43,
        y2: 22.6,
        type: 'image',
        dataId: 'gender',
        initialValue: '/assets/job-simulation/xad/gender/gender_any.png',
        imgStyle: {
          width: '100%',
          height: '100%',
          objectFit: 'contain',
        },
      },
      {
        id: 'audience_age',
        x1: 34.414556962025316,
        y1: 31.035831606137766,
        x2: 45.29272151898734,
        y2: 34.319219332731684,
        type: 'text',
        dataId: 'audience_age',
        initialValue: '18 - 65+',
      },
      {
        id: 'audience_language',
        x1: 34.45411392405063,
        y1: 43.856678919504496,
        x2: 61.94620253164557,
        y2: 53.47231440452954,
        type: 'text',
        dataId: 'audience_language',
        initialValue: 'English',
        style: {
          alignItems: 'start',
        },
        dataDisplayType: 'tag',
        dataSeparator: ', ',
      },
      {
        id: 'audience_location',
        x1: 34.45411392405063,
        y1: 62.14983911052776,
        x2: 61.90664556962025,
        y2: 74.03257564486766,
        type: 'text',
        dataId: 'audience_location',
        initialValue: 'Singapore',
        style: {
          alignItems: 'start',
        },
        dataDisplayType: 'tag',
        dataSeparator: ', ',
      },
      {
        id: 'operation_system_iOS',
        x1: 34.651898734177216,
        y1: 87.71335783900899,
        x2: 35.20569620253164,
        y2: 88.88599631279254,
        type: 'text',
        dataId: 'operation_system',
      },
      {
        id: 'operation_system_android',
        x1: 34.651898734177216,
        y1: 89.27687580405372,
        x2: 35.20569620253164,
        y2: 90.44951427783727,
        type: 'text',
        dataId: 'operation_system',
      },
    ],
  },
];
