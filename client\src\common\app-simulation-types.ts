export interface AppSimulationInputField {
  id: string;
  label: string;
  expectedValue: string;
  // Optional coordinates for clickable input fields (for mode 2)
  x1?: number;
  y1?: number;
  x2?: number;
  y2?: number;
  // Flag to indicate if this input is for mode 2 (clickable input)
  clickable?: boolean;
}

export interface AppSimulationFormatConfig {
  maxValue?: number;
  maxLabel?: string;
  separator?: string;
  template?: string;
}

interface AppSimulationModalInputField {
  id: string;
  type: 'text' | 'textarea' | 'multiSelect' | 'range' | 'radio' | 'checkbox';
  label: string;
  dataId: string;
  required?: boolean;
  // For text/textarea
  placeholder?: string;
  defaultValue?: string | number | [number, number] | string[];
  // For multiSelect
  options?: string[];
  minSelections?: number;
  maxSelections?: number;
  // For range
  min?: number;
  max?: number;
  step?: number;
  labels?: { min: string; max: string };
  // Built-in formatter types (replaces formatValue function)
  formatType?: 'number' | 'currency' | 'range' | 'age-range' | 'percentage' | 'custom';
  formatConfig?: AppSimulationFormatConfig;
  // For radio/checkbox
  radioOptions?: Array<{ value: string; label: string }>;
}

export interface AppSimulationModalConfig {
  title: string;
  description?: string;
  inputs: AppSimulationModalInputField[];
}

export interface AppSimulationElementAction {
  type:
    | 'nextScreen'
    | 'previousScreen'
    | 'inputText'
    | 'inputRange'
    | 'uploadPhoto'
    | 'image'
    | 'dropdown'
    | 'modal'
    | 'triggerMessage'
    | 'checkbox';
  dataContext?: string;
  dataContextId?: string;
  dataContextLabel?: string;
  screenId?: string;
  message?: string;
  withData?: boolean;
  // TODO: inputType legacy, use inputTextType instead
  inputType?: 'input' | 'textarea';
  inputTextType?: 'input' | 'textarea';
  imgPath?: string;
  saveToSelections?: boolean;
  dropdownOptions?: Array<{
    label: string;
    screenId: string;
    dataContext?: string;
    dataContextId?: string;
    saveToSelections?: boolean;
  }>;
  modalConfig?: AppSimulationModalConfig;
}

export interface AppSimulationScreenElement {
  title: string;
  x1: number;
  y1: number;
  x2: number;
  y2: number;
  // Legacy top, left, width, height. Can be calculated based on x1, y1, x2, y2
  left?: number;
  top?: number;
  width?: number;
  height?: number;
  backgroundColor?: string;
  border?: string;
  action?: AppSimulationElementAction;
  actions?: AppSimulationElementAction[];
}

export interface AppSimulationScreenPlaceholder {
  id: string;
  type: 'image' | 'text' | 'checkbox';
  dataId?: string;
  dataDisplayType?: 'text' | 'tag' | 'list' | 'badge';
  dataSeparator?: string;
  checkboxValue?: string; // Value for checkbox type (e.g., 'iOS', 'Android')
  x1: number;
  y1: number;
  x2: number;
  y2: number;
  title?: string;
  initialValue?: string;
  style?: React.CSSProperties;
  imgStyle?: React.CSSProperties;
  dataByTime?: Array<{
    time: number;
    value: string;
  }>;
  increaseByTime?: boolean;
  increaseFrom?: number;
}

export interface AppSimulationScreen {
  id: string;
  title: string;
  image: string;
  bgColor: string;
  triggerMessage?: string;
  elements?: Array<AppSimulationScreenElement>;
  placeholders?: Array<AppSimulationScreenPlaceholder>;
  charts?: Array<{
    id: string;
    type: 'bar' | 'line';
    data?: any;
  }>;
  actions?: Array<{
    type: string;
    message?: string;
    withData?: boolean;
  }>;
}
